from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import datetime

class ValidationStatus(str, Enum):
    VALID = "valid"
    INVALID = "invalid"
    NEEDS_REFINEMENT = "needs_refinement"
    ERROR = "error"

class UserQuery(BaseModel):
    text: str = Field(..., description="User's input query")
    context: Optional[str] = Field(None, description="Additional context for the query")
    validation_strictness: float = Field(0.8, ge=0.0, le=1.0, description="Validation strictness level")

class LogicAssertion(BaseModel):
    statement: str = Field(..., description="The logical statement")
    type: str = Field(..., description="Type of logic (implication, conjunction, etc.)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Extraction confidence")
    variables: List[str] = Field(default_factory=list, description="Variables in the assertion")

class ValidationResult(BaseModel):
    is_valid: bool = Field(..., description="Whether the logic is valid")
    errors: List[str] = Field(default_factory=list, description="Validation errors found")
    warnings: List[str] = Field(default_factory=list, description="Potential issues")
    pyke_output: Optional[Dict[str, Any]] = Field(None, description="Raw PyKe engine output")

class RefinementProposal(BaseModel):
    issue: str = Field(..., description="The logical issue identified")
    suggestion: str = Field(..., description="Suggested correction")
    priority: int = Field(..., ge=1, le=3, description="Priority level (1=high, 3=low)")

class FinalResponse(BaseModel):
    original_query: str
    llm_response: str
    validation_status: ValidationStatus
    assertions_extracted: List[LogicAssertion]
    validation_result: ValidationResult
    refinement_iterations: int
    refinement_proposals: List[RefinementProposal]
    final_validated_response: str
    processing_time: float
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.now)
