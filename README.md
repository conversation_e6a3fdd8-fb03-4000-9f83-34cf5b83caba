# LLM Logic Validator API

This project provides a FastAPI application that validates the logical consistency of LLM (Large Language Model) responses using PyKe, a Python-based knowledge engine. It allows for initial LLM response generation and subsequent refinement based on logical validation feedback.

## Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-repo/API_Logic_pyKE.git
    cd API_Logic_pyKE
    ```

2.  **Create a `.env` file:**
    Create a file named `.env` in the root directory of the project and add the following configuration. Replace the placeholder values with your actual LLM provider details.

    ```dotenv
    # LLM Configuration
    LLM_API_ENDPOINT="https://api.openai.com/v1"  # Replace with custom endpoint if needed
    LLM_API_KEY="your-api-key-here"               # Replace with your actual API key
    LLM_MODEL_NAME="gpt-4"                        # Model to use (e.g. gpt-4, gpt-3.5-turbo)
    LLM_TEMPERATURE=0.3                           # Default creativity level (0.0-1.0)
    ```

## Installation

1.  **Create a virtual environment (recommended):**
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

2.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

## Usage

To run the API, execute the `main_api.py` file:

```bash
python main_api.py
```

The API will typically run on `http://127.0.0.1:8000`. You can access the interactive API documentation (Swagger UI) at `http://127.0.0.1:8000/docs`.

### Sample Request (Validate Logic)

You can send a sample request to the `/validate-logic` endpoint using `curl`.

**Request Body Example:**

```json
{
  "text": "All birds can fly. A penguin is a bird. Therefore, a penguin can fly.",
  "context": "This is a logical reasoning problem."
}
```

**Curl Command:**

```bash
curl -X POST "http://127.0.0.1:8000/validate-logic" \
     -H "Content-Type: application/json" \
     -d '{
           "text": "All birds can fly. A penguin is a bird. Therefore, a penguin can fly.",
           "context": "This is a logical reasoning problem."
         }'
```

This request will send a logical statement to the API for validation, and the API will return a `FinalResponse` indicating the validation status, extracted assertions, and any refinement proposals.