import asyncio
import time
import os
from typing import List
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import logging
from contextlib import asynccontextmanager
import datetime

from config import Config
from llm_interface import LLMInterface
from logic_validator import LogicValidator
from pyke_processor import PyKeProcessor
from refinement_generator import RefinementGenerator
from models import UserQuery, FinalResponse, ValidationStatus, LogicAssertion, ValidationResult

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LogicValidatorAPI:
    def __init__(self):
        self.config = Config()
        self.llm_interface = LLMInterface()
        self.logic_validator = LogicValidator()
        self.pyke_processor = PyKeProcessor(self.config.PYKE_RULES_DIR)
        self.refinement_generator = RefinementGenerator()
        
        # Initialize PyKe rules (will be loaded dynamically based on query)
        self.pyke_processor.load_rules()
    
    async def process_query(self, query: UserQuery) -> FinalResponse:
        """Main processing pipeline for logic validation"""
        start_time = time.time()
        refinement_iterations = 0
        
        try:
            # Step 1: Generate initial LLM response
            logger.info(f"Processing query: {query.text[:50]}...")
            llm_response = await self.llm_interface.generate_response(
                query.text, query.context
            )

            # Step 2: Load appropriate rules based on query
            self.pyke_processor.load_rules(query_text=query.text)

            # Step 3: Start validation loop
            current_response = llm_response
            all_refinement_proposals = []

            for iteration in range(self.config.MAX_REFINEMENT_ITERATIONS):
                refinement_iterations = iteration + 1

                # Extract logic assertions with query context
                assertions = self.logic_validator.extract_logic_assertions(current_response, query.text)

                if not assertions:
                    logger.warning("No logical assertions found in response")
                    break

                # Validate structure
                if not self.logic_validator.validate_structure(assertions):
                    logger.warning("Assertions failed structure validation")
                    break

                # Convert to PyKe format
                pyke_facts = self.logic_validator.format_for_pyke(assertions)

                # Validate with PyKe using query context
                validation_result = self.pyke_processor.evaluate_logic(pyke_facts, query.text)
                
                if validation_result.is_valid:
                    # Logic is valid, exit loop
                    logger.info(f"Logic validated successfully after {iteration + 1} iteration(s)")
                    break
                
                # Generate refinement proposals
                analyzed_errors = self.refinement_generator.analyze_validation_errors(validation_result)
                refinement_proposals = self.refinement_generator.generate_correction_suggestions(analyzed_errors)
                all_refinement_proposals.extend(refinement_proposals)
                
                # Refine response with query context
                if iteration < self.config.MAX_REFINEMENT_ITERATIONS - 1:
                    current_response = await self.llm_interface.refine_response(
                        current_response, refinement_proposals, query.text
                    )
                    logger.info(f"Refined response (iteration {iteration + 1})")
                else:
                    logger.warning("Maximum refinement iterations reached")
            
            # Final validation with query context
            final_assertions = self.logic_validator.extract_logic_assertions(current_response, query.text)
            final_pyke_facts = self.logic_validator.format_for_pyke(final_assertions)
            final_validation = self.pyke_processor.evaluate_logic(final_pyke_facts, query.text)
            
            # Determine final status and generate enhanced final response
            if final_validation.is_valid:
                status = ValidationStatus.VALID
                # Generate a final enhanced response that emphasizes the validated logic
                final_response_text = await self._generate_final_validated_response(
                    query.text, current_response, final_assertions, final_validation
                )
            elif refinement_iterations >= self.config.MAX_REFINEMENT_ITERATIONS:
                status = ValidationStatus.NEEDS_REFINEMENT
                final_response_text = current_response
            else:
                status = ValidationStatus.INVALID
                final_response_text = current_response

            processing_time = time.time() - start_time

            return FinalResponse(
                original_query=query.text,
                llm_response=llm_response,
                validation_status=status,
                assertions_extracted=final_assertions,
                validation_result=final_validation,
                refinement_iterations=refinement_iterations,
                refinement_proposals=all_refinement_proposals,
                final_validated_response=final_response_text,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            processing_time = time.time() - start_time
            
            return FinalResponse(
                original_query=query.text,
                llm_response="Error occurred during processing",
                validation_status=ValidationStatus.ERROR,
                assertions_extracted=[],
                validation_result=ValidationResult(
                    is_valid=False,
                    errors=[str(e)],
                    warnings=[],
                    pyke_output=None
                ),
                refinement_iterations=0,
                refinement_proposals=[],
                final_validated_response="Processing failed",
                processing_time=processing_time
            )

    async def _generate_final_validated_response(self, query: str, response: str,
                                               assertions: List[LogicAssertion],
                                               validation: ValidationResult) -> str:
        """Generate an enhanced final response that emphasizes the validated logic"""
        try:
            # Create a summary of the validation process
            validation_summary = f"""
            Logical Analysis Summary:
            - {len(assertions)} logical assertions identified and validated
            - Domain: {validation.pyke_output.get('domain_detected', 'general') if validation.pyke_output else 'general'}
            - Rules applied: {', '.join(validation.pyke_output.get('rules_applied', [])) if validation.pyke_output else 'basic logic'}
            - Validation status: PASSED
            """

            enhancement_prompt = f"""
            Please enhance the following response by adding a brief logical validation summary at the end.

            Original Query: {query}

            Current Response: {response}

            {validation_summary}

            Instructions:
            1. Keep the original response content intact
            2. Add a brief section at the end that highlights the logical structure has been validated
            3. Mention key logical patterns that were verified (if any)
            4. Keep the enhancement concise and professional
            """

            enhanced_response = await self.llm_interface.generate_response(enhancement_prompt)
            return enhanced_response

        except Exception as e:
            logger.error(f"Failed to generate enhanced final response: {e}")
            return response  # Return original response if enhancement fails

# FastAPI application
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting Logic Validator API")
    yield
    # Shutdown
    logger.info("Shutting down Logic Validator API")

app = FastAPI(
    title="LLM Logic Validator API",
    description="API for validating logical consistency in LLM responses using PyKe",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize API controller
api_controller = LogicValidatorAPI()

@app.post("/validate-logic", response_model=FinalResponse)
async def validate_logic_response(query: UserQuery):
    """Main endpoint for logic validation"""
    try:
        result = await api_controller.process_query(query)
        return result
    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/extract-logic")
async def extract_logic(text: str):
    """Extract logical assertions from text"""
    try:
        assertions = api_controller.logic_validator.extract_logic_assertions(text)
        return {"assertions": assertions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/validate-assertions")
async def validate_assertions(assertions: List[LogicAssertion]):
    """Validate logical assertions directly"""
    try:
        pyke_facts = api_controller.logic_validator.format_for_pyke(assertions)
        validation_result = api_controller.pyke_processor.evaluate_logic(pyke_facts)
        return validation_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/rules")
async def list_rules():
    """List available logic rules"""
    try:
        rules_dir = api_controller.config.PYKE_RULES_DIR
        rule_files = [f for f in os.listdir(rules_dir) if f.endswith('.krb')]
        return {"rules_directory": rules_dir, "rule_files": rule_files}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.datetime.now()}

if __name__ == "__main__":
    import uvicorn
    config = Config()
    uvicorn.run(
        "main_api:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=True
    )
