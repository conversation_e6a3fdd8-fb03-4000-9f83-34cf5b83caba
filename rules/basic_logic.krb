# basic_logic.krb - Basic logical validation rules

# Consistency rules
consistency_check
    use consistency_rule($fact1, $fact2)
    when
        implies($x, $y)
        not_fact($y)
        fact($x)
    assert
        contradiction($fact1, $fact2)

# Transitivity rule
transitivity
    use transitivity_rule($x, $y, $z)
    when
        implies($x, $y)
        implies($y, $z)
    assert
        implies($x, $z)

# Modus ponens
modus_ponens
    use mp_rule($x, $y)
    when
        implies($x, $y)
        fact($x)
    assert
        fact($y)

# Universal instantiation
universal_instantiation
    use ui_rule($x, $property)
    when
        forall($var, $property)
        instance($x, $var)
    assert
        has_property($x, $property)
