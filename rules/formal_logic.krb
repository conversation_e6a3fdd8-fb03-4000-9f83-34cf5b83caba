# formal_logic.krb - Formal logic reasoning rules

# Propositional logic rules
modus_ponens_rule
    use mp($p, $q)
    when
        implies($p, $q)
        fact($p)
    assert
        fact($q)

modus_tollens_rule
    use mt($p, $q)
    when
        implies($p, $q)
        not_fact($q)
    assert
        not_fact($p)

# Hypothetical syllogism
hypothetical_syllogism
    use hs($p, $q, $r)
    when
        implies($p, $q)
        implies($q, $r)
    assert
        implies($p, $r)

# Disjunctive syllogism
disjunctive_syllogism
    use ds($p, $q)
    when
        or_fact($p, $q)
        not_fact($p)
    assert
        fact($q)

# Conjunction rules
conjunction_introduction
    use conj_intro($p, $q)
    when
        fact($p)
        fact($q)
    assert
        and_fact($p, $q)

conjunction_elimination
    use conj_elim($p, $q)
    when
        and_fact($p, $q)
    assert
        fact($p)
        fact($q)

# Disjunction rules
disjunction_introduction
    use disj_intro($p, $q)
    when
        fact($p)
    assert
        or_fact($p, $q)

# Predicate logic rules
universal_instantiation
    use ui($x, $property)
    when
        forall($var, $property)
        instance($x, $var)
    assert
        has_property($x, $property)

universal_generalization
    use ug($property, $domain)
    when
        arbitrary_instance($x, $domain)
        has_property($x, $property)
    assert
        forall($domain, $property)

existential_instantiation
    use ei($property, $witness)
    when
        exists($var, $property)
    assert
        has_property($witness, $property)

existential_generalization
    use eg($x, $property)
    when
        has_property($x, $property)
    assert
        exists($var, $property)

# Logical equivalences
double_negation
    use double_neg($p)
    when
        not_fact(not_fact($p))
    assert
        fact($p)

de_morgan_law1
    use demorgan1($p, $q)
    when
        not_fact(and_fact($p, $q))
    assert
        or_fact(not_fact($p), not_fact($q))

de_morgan_law2
    use demorgan2($p, $q)
    when
        not_fact(or_fact($p, $q))
    assert
        and_fact(not_fact($p), not_fact($q))

# Contradiction detection
contradiction_rule
    use contradiction($p)
    when
        fact($p)
        not_fact($p)
    assert
        logical_contradiction($p)

# Tautology recognition
tautology_rule
    use tautology($statement)
    when
        logical_statement($statement)
        always_true($statement)
    assert
        tautology($statement)

# Consistency checking
consistency_rule
    use consistency($set)
    when
        statement_set($set)
        no_contradictions($set)
    assert
        consistent_set($set)
