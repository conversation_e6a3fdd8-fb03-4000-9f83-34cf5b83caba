##############################################################################
# Facts
##############################################################################

# Input observations or raw facts from the environment
Fact input_data(observation).

# Knowledge base for problem classification and solutions
Fact problem(problem_type, symptom).
Fact solution(problem_type, proposed_solution).
Fact constraint(problem_type, solution, constraint).

# Tracks applied solutions and resolution status
Fact applied_solution(problem_type, solution).
Fact resolution_status(problem_type, status).

##############################################################################
# Rules
##############################################################################

# Rule 1: Identify the problem based on input observations
# If the observation matches with a known symptom, classify the problem.
Rule identify_problem(problem_type) <= (
    input_data(observation),
    problem(problem_type, observation)
)

# Rule 2: Suggest a possible solution for an identified problem type
# If a problem is identified, suggest a solution.
Rule suggest_solution(problem_type, solution) <= (
    identify_problem(problem_type),
    solution(problem_type, solution)
)

# Rule 3: Check constraints for the suggested solution
# Ensure the solution satisfies all constraints.
Rule check_solution_constraints(problem_type, solution) <= (
    suggest_solution(problem_type, solution),
    not constraint(problem_type, solution, not_satisfied),
    satisfies_constraints(solution)
)

# Rule 4: Mark the problem as resolved if the solution is valid
Rule resolve_problem(problem_type) <= (
    check_solution_constraints(problem_type, solution),
    apply_solution(problem_type, solution),
    mark_resolved(problem_type)
)

# Rule 5: Handle fallback/alternative solutions
# If a solution fails, attempt alternative solutions.
Rule fallback_solution(problem_type, alt_solution) <= (
    not resolve_problem(problem_type),
    suggest_solution(problem_type, alt_solution),
    alt_solution != solution,  # Ensure alternative solution is distinct
    apply_solution(problem_type, alt_solution)
)

# Rule 6: Validation and logging
# Log the reasoning process and validate if the problem is truly resolved.
Rule validate_resolution(problem_type) <= (
    mark_resolved(problem_type),
    validate_solution(problem_type),
    resolution_status(problem_type, "resolved")
)

##############################################################################
# Helper Rules and Functions
##############################################################################

# Mark a problem as resolved
Rule mark_resolved(problem_type) <= (
    resolution_status(problem_type, "resolved")
)

# Check if a solution satisfies constraints
Rule satisfies_constraints(solution) <= (
    not constraint(_, solution, not_satisfied)
)

# Apply a solution to the current problem
Rule apply_solution(problem_type, solution) <= (
    not applied_solution(problem_type, solution),
    applied_solution(problem_type, solution)  # Track applied solution
)

# Validate the effectiveness of the solution
Rule validate_solution(problem_type) <= (
    resolution_status(problem_type, "resolved")
)