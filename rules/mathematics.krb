# mathematics.krb - Mathematical reasoning rules

# Mathematical equality rules
mathematical_equality
    use equality_rule($x, $y, $z)
    when
        equals($x, $y)
        equals($y, $z)
    assert
        equals($x, $z)

# Mathematical proof validation
proof_validity
    use proof_rule($premise, $conclusion)
    when
        mathematical_fact($premise)
        proves($premise, $conclusion)
    assert
        mathematical_fact($conclusion)

# Algebraic manipulation rules
algebraic_consistency
    use algebra_rule($equation1, $equation2)
    when
        equation($equation1)
        derived_from($equation2, $equation1)
    assert
        algebraically_valid($equation2)

# Geometric reasoning
geometric_property
    use geometry_rule($shape, $property)
    when
        geometric_object($shape)
        has_property($shape, $property)
        geometric_axiom($property)
    assert
        geometric_fact($shape, $property)

# Mathematical induction
induction_rule
    use induction($base_case, $inductive_step)
    when
        proves_base_case($base_case)
        proves_inductive_step($inductive_step)
    assert
        mathematical_theorem($base_case, $inductive_step)

# Function properties
function_property
    use function_rule($function, $domain, $range)
    when
        function_definition($function, $domain, $range)
        valid_domain($domain)
        valid_range($range)
    assert
        well_defined_function($function)

# Limit and continuity
limit_rule
    use limit_validation($function, $point, $limit_value)
    when
        function($function)
        approaches($function, $point, $limit_value)
        epsilon_delta_proof($function, $point, $limit_value)
    assert
        limit_exists($function, $point, $limit_value)

# Mathematical contradiction detection
math_contradiction
    use math_contradiction_rule($statement1, $statement2)
    when
        mathematical_fact($statement1)
        mathematical_fact($statement2)
        contradicts($statement1, $statement2)
    assert
        mathematical_contradiction($statement1, $statement2)
