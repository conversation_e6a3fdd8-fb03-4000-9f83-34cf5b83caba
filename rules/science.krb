# science.krb - Scientific reasoning rules

# Hypothesis testing
hypothesis_validation
    use hypothesis_rule($hypothesis, $evidence)
    when
        scientific_hypothesis($hypothesis)
        empirical_evidence($evidence)
        supports($evidence, $hypothesis)
    assert
        supported_hypothesis($hypothesis)

# Experimental design
experiment_validity
    use experiment_rule($experiment, $controls, $variables)
    when
        experimental_design($experiment)
        proper_controls($controls)
        controlled_variables($variables)
    assert
        valid_experiment($experiment)

# Causal relationships
causal_inference
    use causation_rule($cause, $effect, $mechanism)
    when
        observed_correlation($cause, $effect)
        plausible_mechanism($mechanism)
        eliminates_confounders($cause, $effect)
    assert
        causal_relationship($cause, $effect)

# Scientific method
scientific_method_rule
    use method_rule($observation, $hypothesis, $test, $conclusion)
    when
        empirical_observation($observation)
        testable_hypothesis($hypothesis)
        experimental_test($test)
        evidence_based_conclusion($conclusion)
    assert
        scientific_reasoning($observation, $hypothesis, $test, $conclusion)

# Peer review and replication
scientific_validity
    use validity_rule($study, $replication)
    when
        scientific_study($study)
        peer_reviewed($study)
        replicated_results($replication)
    assert
        scientifically_valid($study)

# Theory formation
theory_validation
    use theory_rule($theory, $evidence_set)
    when
        scientific_theory($theory)
        multiple_evidence($evidence_set)
        explains_phenomena($theory, $evidence_set)
        makes_predictions($theory)
    assert
        well_supported_theory($theory)

# Data analysis
data_validity
    use data_rule($data, $analysis, $conclusion)
    when
        empirical_data($data)
        appropriate_analysis($analysis)
        statistical_significance($analysis)
        valid_interpretation($conclusion)
    assert
        reliable_conclusion($conclusion)

# Scientific contradiction detection
science_contradiction
    use science_contradiction_rule($finding1, $finding2)
    when
        scientific_finding($finding1)
        scientific_finding($finding2)
        contradictory_results($finding1, $finding2)
    assert
        requires_further_investigation($finding1, $finding2)

# Evidence hierarchy
evidence_strength
    use evidence_hierarchy($evidence, $strength)
    when
        scientific_evidence($evidence)
        evidence_type($evidence, $type)
        strength_level($type, $strength)
    assert
        evidence_quality($evidence, $strength)

# Falsifiability
falsifiability_rule
    use falsifiable_rule($hypothesis)
    when
        scientific_hypothesis($hypothesis)
        testable_predictions($hypothesis)
        potential_falsifiers($hypothesis)
    assert
        falsifiable_hypothesis($hypothesis)
