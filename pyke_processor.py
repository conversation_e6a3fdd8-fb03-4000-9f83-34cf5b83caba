import os
import tempfile
import subprocess
import re
from typing import List, Dict, Any, Optional
import logging
from models import ValidationResult

logger = logging.getLogger(__name__)

class PyKeProcessor:
    def __init__(self, rules_dir: str = "./rules"):
        self.rules_dir = rules_dir
        self.engine = None
        self.loaded_rules = {}
        self.domain_rules = {
            'mathematics': ['basic_logic.krb', 'mathematics.krb', 'formal_logic.krb'],
            'science': ['basic_logic.krb', 'science.krb', 'formal_logic.krb'],
            'logic': ['basic_logic.krb', 'formal_logic.krb', 'deduction.krb'],
            'problem_solving': ['basic_logic.krb', 'problem_resolver.krb'],
            'general': ['basic_logic.krb', 'formal_logic.krb']
        }
        self._ensure_rules_directory()
    
    def _ensure_rules_directory(self):
        """Ensure rules directory exists and has basic rules"""
        os.makedirs(self.rules_dir, exist_ok=True)
        
        # Create basic logic rules if they don't exist
        basic_rules_file = os.path.join(self.rules_dir, "basic_logic.krb")
        if not os.path.exists(basic_rules_file):
            self._create_basic_rules()
    
    def _create_basic_rules(self):
        """Create basic logical validation rules"""
        basic_rules = """
# basic_logic.krb - Basic logical validation rules

# Consistency rules
consistency_check
    use consistency_rule($fact1, $fact2)
    when
        implies($x, $y)
        not_fact($y)
        fact($x)
    assert
        contradiction($fact1, $fact2)

# Transitivity rule
transitivity
    use transitivity_rule($x, $y, $z)
    when
        implies($x, $y)
        implies($y, $z)
    assert
        implies($x, $z)

# Modus ponens
modus_ponens
    use mp_rule($x, $y)
    when
        implies($x, $y)
        fact($x)
    assert
        fact($y)

# Universal instantiation
universal_instantiation
    use ui_rule($x, $property)
    when
        forall($var, $property)
        instance($x, $var)
    assert
        has_property($x, $property)
"""
        
        with open(os.path.join(self.rules_dir, "basic_logic.krb"), 'w') as f:
            f.write(basic_rules)
        
        # Create corresponding facts file
        facts_file = """
# basic_logic.kfb - Basic facts for logic validation

# Define some basic instances for testing
instance(socrates, human)
instance(aristotle, human)
forall(human, mortal)
"""
        
        with open(os.path.join(self.rules_dir, "basic_logic.kfb"), 'w') as f:
            f.write(facts_file)
    
    def detect_query_domain(self, query_text: str) -> str:
        """Detect the domain of the user query to select appropriate rules"""
        query_lower = query_text.lower()

        # Mathematics keywords
        math_keywords = ['equation', 'solve', 'calculate', 'number', 'algebra', 'geometry',
                        'theorem', 'proof', 'derivative', 'integral', 'function']

        # Science keywords
        science_keywords = ['experiment', 'hypothesis', 'theory', 'observation', 'data',
                           'analysis', 'scientific', 'research', 'study', 'evidence']

        # Logic keywords
        logic_keywords = ['if', 'then', 'implies', 'therefore', 'because', 'since',
                         'all', 'some', 'every', 'exists', 'not', 'and', 'or']

        # Problem solving keywords
        problem_keywords = ['problem', 'solution', 'resolve', 'fix', 'troubleshoot',
                           'issue', 'error', 'debug', 'analyze']

        # Count keyword matches
        math_score = sum(1 for keyword in math_keywords if keyword in query_lower)
        science_score = sum(1 for keyword in science_keywords if keyword in query_lower)
        logic_score = sum(1 for keyword in logic_keywords if keyword in query_lower)
        problem_score = sum(1 for keyword in problem_keywords if keyword in query_lower)

        # Determine domain based on highest score
        scores = {
            'mathematics': math_score,
            'science': science_score,
            'logic': logic_score,
            'problem_solving': problem_score
        }

        max_domain = max(scores, key=scores.get)
        if scores[max_domain] > 0:
            return max_domain
        return 'general'

    def load_rules(self, rule_files: List[str] = None, query_text: str = None) -> bool:
        """Load PyKe rules from files, optionally adapting to query domain"""
        try:
            if query_text:
                domain = self.detect_query_domain(query_text)
                rule_files = self.domain_rules.get(domain, self.domain_rules['general'])
                logger.info(f"Detected domain: {domain}, loading rules: {rule_files}")
            elif rule_files is None:
                rule_files = self.domain_rules['general']

            # Load and cache rules
            for rule_file in rule_files:
                rule_path = os.path.join(self.rules_dir, rule_file)
                if os.path.exists(rule_path):
                    with open(rule_path, 'r') as f:
                        self.loaded_rules[rule_file] = f.read()
                    logger.info(f"Loaded rule file: {rule_file}")
                else:
                    logger.warning(f"Rule file not found: {rule_path}")

            return True
        except Exception as e:
            logger.error(f"Failed to load PyKe rules: {e}")
            return False
    
    def evaluate_logic(self, pyke_facts: List[str], query_context: str = None) -> ValidationResult:
        """Evaluate logical assertions using PyKe engine with context-aware validation"""
        try:
            # Load appropriate rules if query context is provided
            if query_context and not self.loaded_rules:
                self.load_rules(query_text=query_context)

            # Create temporary facts file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.kfb', delete=False) as temp_file:
                for fact in pyke_facts:
                    temp_file.write(f"{fact}\n")
                temp_file_path = temp_file.name

            # Enhanced PyKe validation with loaded rules
            validation_result = self._simulate_pyke_validation(pyke_facts, query_context)

            # Clean up
            os.unlink(temp_file_path)

            return validation_result

        except Exception as e:
            logger.error(f"PyKe evaluation error: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[f"PyKe evaluation failed: {str(e)}"],
                warnings=[],
                pyke_output=None
            )
    
    def _simulate_pyke_validation(self, pyke_facts: List[str], query_context: str = None) -> ValidationResult:
        """Enhanced PyKe validation logic with rule-based checking"""
        errors = []
        warnings = []

        # Basic logical consistency checks
        implications = [fact for fact in pyke_facts if 'implies' in fact]
        universals = [fact for fact in pyke_facts if 'forall' in fact]
        conjunctions = [fact for fact in pyke_facts if 'and_fact' in fact]

        # Enhanced contradiction checking using loaded rules
        if self._check_contradictions(pyke_facts):
            errors.append("Logical contradiction detected in assertions")

        # Check for circular reasoning
        if self._check_circular_reasoning(implications):
            warnings.append("Potential circular reasoning detected")

        # Check for unsupported conclusions
        unsupported = self._check_unsupported_conclusions(pyke_facts)
        if unsupported:
            warnings.extend(unsupported)

        # Rule-based validation using loaded rules
        rule_violations = self._check_rule_violations(pyke_facts, query_context)
        if rule_violations:
            errors.extend(rule_violations)

        # Domain-specific validation
        domain_issues = self._check_domain_specific_issues(pyke_facts, query_context)
        if domain_issues:
            warnings.extend(domain_issues)

        is_valid = len(errors) == 0

        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            pyke_output={
                "facts_processed": len(pyke_facts),
                "implications_found": len(implications),
                "universals_found": len(universals),
                "conjunctions_found": len(conjunctions),
                "rules_applied": list(self.loaded_rules.keys()),
                "domain_detected": self.detect_query_domain(query_context) if query_context else "unknown"
            }
        )
    
    def _check_contradictions(self, facts: List[str]) -> bool:
        """Check for logical contradictions"""
        # Simplified contradiction detection
        positive_facts = set()
        negative_facts = set()
        
        for fact in facts:
            if 'not_fact' in fact:
                # Extract the negated fact
                content = fact.split("'")[1] if "'" in fact else fact
                negative_facts.add(content.lower())
            else:
                content = fact.split("'")[1] if "'" in fact else fact
                positive_facts.add(content.lower())
        
        # Check for direct contradictions
        return bool(positive_facts.intersection(negative_facts))
    
    def _check_circular_reasoning(self, implications: List[str]) -> bool:
        """Check for circular reasoning in implications"""
        # Simplified circular reasoning detection
        if len(implications) < 2:
            return False
        
        # Look for patterns like A->B, B->A
        implication_pairs = []
        for impl in implications:
            # Extract variables (simplified)
            if 'implies(' in impl:
                content = impl.split('implies(')[1].split(')')[0]
                vars_part = content.split(',')[0] if ',' in content else content
                implication_pairs.append(vars_part.strip())
        
        # Check for simple circular patterns
        for i, pair1 in enumerate(implication_pairs):
            for j, pair2 in enumerate(implication_pairs[i+1:], i+1):
                if pair1 in pair2 and pair2 in pair1:
                    return True
        
        return False
    
    def _check_unsupported_conclusions(self, facts: List[str]) -> List[str]:
        """Check for conclusions without sufficient support"""
        warnings = []
        
        # Count different types of logical structures
        implications = len([f for f in facts if 'implies' in f])
        universals = len([f for f in facts if 'forall' in f])
        basic_facts = len([f for f in facts if 'fact(' in f])
        
        # Warn if there are many conclusions but few supporting facts
        if implications > basic_facts * 2:
            warnings.append("High ratio of implications to supporting facts")
        
        if universals > 0 and basic_facts == 0:
            warnings.append("Universal statements without supporting instances")
        
        return warnings

    def _check_rule_violations(self, facts: List[str], query_context: str = None) -> List[str]:
        """Check for violations of loaded rules"""
        violations = []

        if not self.loaded_rules:
            return violations

        # Check modus ponens violations
        implications = [f for f in facts if 'implies(' in f]
        basic_facts = [f for f in facts if 'fact(' in f and 'implies(' not in f]

        for impl in implications:
            # Extract antecedent and consequent (simplified)
            try:
                content = impl.split('implies(')[1].split(')')[0]
                if ',' in content:
                    antecedent = content.split(',')[0].strip()
                    # Check if antecedent is asserted but consequent is missing
                    antecedent_asserted = any(antecedent in fact for fact in basic_facts)
                    if antecedent_asserted:
                        # Should have consequent, but this is simplified logic
                        pass
            except:
                continue

        # Check for universal instantiation violations
        universals = [f for f in facts if 'forall(' in f]
        instances = [f for f in facts if 'instance(' in f]

        if universals and not instances:
            violations.append("Universal statements without specific instances may be unverifiable")

        return violations

    def _check_domain_specific_issues(self, facts: List[str], query_context: str = None) -> List[str]:
        """Check for domain-specific logical issues"""
        issues = []

        if not query_context:
            return issues

        domain = self.detect_query_domain(query_context)

        if domain == 'mathematics':
            # Check for mathematical reasoning issues
            if any('all' in fact.lower() for fact in facts):
                if not any('some' in fact.lower() or 'exists' in fact.lower() for fact in facts):
                    issues.append("Mathematical reasoning may benefit from specific examples")

        elif domain == 'science':
            # Check for scientific reasoning issues
            if any('hypothesis' in query_context.lower() or 'theory' in query_context.lower()):
                if not any('evidence' in fact.lower() or 'observation' in fact.lower() for fact in facts):
                    issues.append("Scientific claims should be supported by evidence or observations")

        elif domain == 'problem_solving':
            # Check for problem-solving reasoning issues
            if 'problem(' in str(facts):
                if not any('solution(' in fact for fact in facts):
                    issues.append("Problem identified but no solution proposed")

        return issues
