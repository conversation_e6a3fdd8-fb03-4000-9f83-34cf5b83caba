#!/usr/bin/env python3
"""
Demo script showing the complete workflow implementation as described in diag.txt

This script demonstrates:
1. User Input → LLM Generates Initial Response
2. Extract Logic/Assertions → Logic Validator  
3. Pass to PyKe Logic Engine
4. Evaluate Logic/Rules → ValidationResult
5. If invalid → Refinement Proposal Generator → LLM (loop)
6. If valid → Final LLM Response
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import UserQuery
from main_api import LogicValidatorAPI

async def demonstrate_workflow():
    """Demonstrate the complete workflow with various test cases"""
    
    print("=" * 80)
    print("LOGIC VALIDATOR WORKFLOW DEMONSTRATION")
    print("=" * 80)
    print()
    
    # Initialize the API
    try:
        api = LogicValidatorAPI()
        print("✓ Logic Validator API initialized successfully")
    except Exception as e:
        print(f"✗ Failed to initialize API: {e}")
        return
    
    # Test cases representing different domains and complexity levels
    test_cases = [
        {
            "name": "Valid Mathematical Logic",
            "query": UserQuery(
                text="If x = 5 and y = 3, then x + y = 8. Since addition is commutative, y + x also equals 8.",
                context="Mathematical reasoning with valid logic",
                validation_strictness=0.8
            ),
            "expected": "Should validate successfully with minimal refinement"
        },
        {
            "name": "Scientific Hypothesis Testing",
            "query": UserQuery(
                text="The hypothesis that exercise improves cardiovascular health is supported by multiple controlled studies showing reduced heart disease rates in active populations.",
                context="Scientific reasoning with evidence",
                validation_strictness=0.8
            ),
            "expected": "Should validate scientific reasoning structure"
        },
        {
            "name": "Problematic Logic (Classic Example)",
            "query": UserQuery(
                text="All birds can fly. Penguins are birds. Therefore, penguins can fly.",
                context="Testing error detection and refinement",
                validation_strictness=0.8
            ),
            "expected": "Should detect logical error and attempt refinement"
        },
        {
            "name": "Complex Logical Reasoning",
            "query": UserQuery(
                text="If all humans are mortal, and Socrates is human, then Socrates is mortal. This follows from universal instantiation in predicate logic.",
                context="Formal logical reasoning",
                validation_strictness=0.9
            ),
            "expected": "Should validate formal logical structure"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {test_case['name']}")
        print(f"{'='*60}")
        print(f"Query: {test_case['query'].text}")
        print(f"Expected: {test_case['expected']}")
        print()
        
        try:
            # Process through the complete workflow
            print("🔄 Processing through workflow...")
            result = await api.process_query(test_case['query'])
            
            # Display workflow results
            print(f"📊 WORKFLOW RESULTS:")
            print(f"   Status: {result.validation_status.upper()}")
            print(f"   Processing Time: {result.processing_time:.2f}s")
            print(f"   Refinement Iterations: {result.refinement_iterations}")
            print(f"   Assertions Extracted: {len(result.assertions_extracted)}")
            
            # Show domain detection and rules applied
            if result.validation_result.pyke_output:
                domain = result.validation_result.pyke_output.get('domain_detected', 'general')
                rules = result.validation_result.pyke_output.get('rules_applied', [])
                print(f"   Domain Detected: {domain}")
                print(f"   Rules Applied: {', '.join(rules) if rules else 'basic logic'}")
            
            # Show validation details
            if result.validation_result.errors:
                print(f"   ❌ Errors Found: {len(result.validation_result.errors)}")
                for error in result.validation_result.errors:
                    print(f"      - {error}")
            
            if result.validation_result.warnings:
                print(f"   ⚠️  Warnings: {len(result.validation_result.warnings)}")
                for warning in result.validation_result.warnings:
                    print(f"      - {warning}")
            
            # Show refinement process
            if result.refinement_proposals:
                print(f"   🔧 Refinement Proposals: {len(result.refinement_proposals)}")
                for j, proposal in enumerate(result.refinement_proposals, 1):
                    priority = ["HIGH", "MEDIUM", "LOW"][proposal.priority - 1]
                    print(f"      {j}. [{priority}] {proposal.issue}")
                    print(f"         → {proposal.suggestion}")
            
            # Show final response comparison
            print(f"\n📝 RESPONSE COMPARISON:")
            print(f"   Original LLM Response Length: {len(result.llm_response)} chars")
            print(f"   Final Validated Response Length: {len(result.final_validated_response)} chars")
            
            if result.validation_status == "valid":
                print("   ✅ Logic successfully validated!")
            elif result.validation_status == "needs_refinement":
                print("   🔄 Logic needs further refinement")
            else:
                print("   ❌ Logic validation failed")
                
        except Exception as e:
            print(f"   ❌ Error processing test case: {e}")
    
    print(f"\n{'='*80}")
    print("WORKFLOW DEMONSTRATION COMPLETE")
    print(f"{'='*80}")
    print("\nWorkflow Summary:")
    print("1. ✓ User Input → LLM Initial Response")
    print("2. ✓ Logic Assertion Extraction (domain-aware)")
    print("3. ✓ PyKe Logic Engine Evaluation (rule-based)")
    print("4. ✓ Validation Result Analysis")
    print("5. ✓ Refinement Loop (if needed)")
    print("6. ✓ Final Enhanced Response Generation")
    print("\nThe implementation successfully follows the flowchart workflow!")

def main():
    """Main entry point"""
    try:
        asyncio.run(demonstrate_workflow())
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\nDemo failed with error: {e}")

if __name__ == "__main__":
    main()
