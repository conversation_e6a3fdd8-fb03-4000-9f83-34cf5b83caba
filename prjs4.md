*Thinking...*

> This is a comprehensive request for implementing a Python API that integrates Large Language Model responses with logical validation using PyKe. The user wants me to create a complete system with multiple components. Let me break this down:
> 
> 1. LLM Interface Module - for interacting with LLMs
> 2. Logic Validator Class - for extracting and formatting logical assertions
> 3. PyKe Logic Engine Integration - for evaluating logic
> 4. Refinement Proposal Generator - for suggesting corrections
> 5. Main API Controller - orchestrating the flow
> 6. API endpoints using FastAPI
> 7. Comprehensive error handling and testing
> 
> This is a substantial implementation that requires:
> - Understanding of PyKe logic engine
> - LLM integration (I'll use OpenAI as an example)
> - Logic extraction from natural language
> - FastAPI for the API layer
> - Pydantic for data models
> - Proper error handling and validation
> 
> I'll implement this step by step, starting with the data models, then the core components, and finally the API layer.

I'll implement a comprehensive Python API system for LLM logic validation using PyKe. This is a sophisticated architecture that combines natural language processing with formal logic validation.

```python
# requirements.txt
"""
pyke3==1.1.1
openai>=1.0.0
fastapi>=0.104.0
pydantic>=2.0.0
uvicorn>=0.24.0
python-multipart>=0.0.6
asyncio-timeout>=4.0.0
regex>=2023.0.0
nltk>=3.8.0
"""

# models.py
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import datetime

class ValidationStatus(str, Enum):
    VALID = "valid"
    INVALID = "invalid"
    NEEDS_REFINEMENT = "needs_refinement"
    ERROR = "error"

class UserQuery(BaseModel):
    text: str = Field(..., description="User's input query")
    context: Optional[str] = Field(None, description="Additional context for the query")
    validation_strictness: float = Field(0.8, ge=0.0, le=1.0, description="Validation strictness level")

class LogicAssertion(BaseModel):
    statement: str = Field(..., description="The logical statement")
    type: str = Field(..., description="Type of logic (implication, conjunction, etc.)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Extraction confidence")
    variables: List[str] = Field(default_factory=list, description="Variables in the assertion")

class ValidationResult(BaseModel):
    is_valid: bool = Field(..., description="Whether the logic is valid")
    errors: List[str] = Field(default_factory=list, description="Validation errors found")
    warnings: List[str] = Field(default_factory=list, description="Potential issues")
    pyke_output: Optional[Dict[str, Any]] = Field(None, description="Raw PyKe engine output")

class RefinementProposal(BaseModel):
    issue: str = Field(..., description="The logical issue identified")
    suggestion: str = Field(..., description="Suggested correction")
    priority: int = Field(..., ge=1, le=3, description="Priority level (1=high, 3=low)")

class FinalResponse(BaseModel):
    original_query: str
    llm_response: str
    validation_status: ValidationStatus
    assertions_extracted: List[LogicAssertion]
    validation_result: ValidationResult
    refinement_iterations: int
    refinement_proposals: List[RefinementProposal]
    final_validated_response: str
    processing_time: float
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.now)

# config.py
from typing import Dict, Any
import os

class Config:
    # LLM Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4")
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
    
    # Validation Configuration
    MAX_REFINEMENT_ITERATIONS = int(os.getenv("MAX_REFINEMENT_ITERATIONS", "3"))
    VALIDATION_TIMEOUT = int(os.getenv("VALIDATION_TIMEOUT", "30"))
    
    # PyKe Configuration
    PYKE_RULES_DIR = os.getenv("PYKE_RULES_DIR", "./rules")
    
    # API Configuration
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8000"))

# llm_interface.py
import openai
import asyncio
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class LLMInterface:
    def __init__(self, api_key: str, model: str = "gpt-4", temperature: float = 0.3):
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = model
        self.temperature = temperature
    
    async def generate_response(self, user_input: str, context: Optional[str] = None) -> str:
        """Generate initial response from LLM"""
        try:
            system_prompt = """You are a logical reasoning assistant. Provide clear, 
            logically structured responses. Use explicit logical connections and 
            reasoning steps in your answers."""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Context: {context}\n\nQuery: {user_input}" if context else user_input}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=1500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            raise Exception(f"Failed to generate LLM response: {e}")
    
    async def refine_response(self, original_response: str, refinement_proposals: List[RefinementProposal]) -> str:
        """Refine LLM response based on logical validation feedback"""
        try:
            refinement_text = "\n".join([
                f"- {proposal.issue}: {proposal.suggestion}" 
                for proposal in sorted(refinement_proposals, key=lambda x: x.priority)
            ])
            
            refinement_prompt = f"""
            Please revise the following response to address these logical issues:
            
            Original Response:
            {original_response}
            
            Issues to Address:
            {refinement_text}
            
            Provide a logically sound revision that maintains the core meaning while fixing the identified issues.
            """
            
            messages = [
                {"role": "system", "content": "You are a logical reasoning expert. Revise responses to be logically consistent."},
                {"role": "user", "content": refinement_prompt}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature * 0.8,  # Lower temperature for refinement
                max_tokens=1500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"LLM refinement error: {e}")
            raise Exception(f"Failed to refine LLM response: {e}")

# logic_validator.py
import re
import nltk
from typing import List, Dict, Tuple
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.tag import pos_tag

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

class LogicValidator:
    def __init__(self):
        # Logic patterns for extraction
        self.logic_patterns = {
            'implication': [
                r'if\s+(.+?)\s+then\s+(.+?)(?:\.|$)',
                r'(.+?)\s+implies\s+(.+?)(?:\.|$)',
                r'when\s+(.+?),\s*(.+?)(?:\.|$)'
            ],
            'conjunction': [
                r'(.+?)\s+and\s+(.+?)(?:\.|$)',
                r'both\s+(.+?)\s+and\s+(.+?)(?:\.|$)'
            ],
            'disjunction': [
                r'(.+?)\s+or\s+(.+?)(?:\.|$)',
                r'either\s+(.+?)\s+or\s+(.+?)(?:\.|$)'
            ],
            'negation': [
                r'not\s+(.+?)(?:\.|$)',
                r'it\s+is\s+not\s+the\s+case\s+that\s+(.+?)(?:\.|$)'
            ],
            'universal': [
                r'all\s+(.+?)\s+are\s+(.+?)(?:\.|$)',
                r'every\s+(.+?)\s+is\s+(.+?)(?:\.|$)'
            ],
            'existential': [
                r'some\s+(.+?)\s+are\s+(.+?)(?:\.|$)',
                r'there\s+exists?\s+(.+?)(?:\.|$)'
            ]
        }
    
    def extract_logic_assertions(self, llm_response: str) -> List[LogicAssertion]:
        """Extract logical assertions from LLM response"""
        assertions = []
        sentences = sent_tokenize(llm_response)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # Skip very short sentences
                continue
                
            for logic_type, patterns in self.logic_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, sentence, re.IGNORECASE)
                    for match in matches:
                        assertion = LogicAssertion(
                            statement=sentence,
                            type=logic_type,
                            confidence=self._calculate_confidence(sentence, logic_type),
                            variables=self._extract_variables(sentence)
                        )
                        assertions.append(assertion)
                        break
                if assertions and assertions[-1].statement == sentence:
                    break
        
        return assertions
    
    def _calculate_confidence(self, sentence: str, logic_type: str) -> float:
        """Calculate confidence score for logic extraction"""
        # Basic confidence calculation based on pattern strength and sentence structure
        base_confidence = 0.7
        
        # Boost confidence for clear logical indicators
        logical_indicators = ['therefore', 'thus', 'hence', 'consequently', 'because']
        if any(indicator in sentence.lower() for indicator in logical_indicators):
            base_confidence += 0.2
        
        # Reduce confidence for complex sentences
        if len(sentence.split()) > 20:
            base_confidence -= 0.1
        
        return min(max(base_confidence, 0.0), 1.0)
    
    def _extract_variables(self, sentence: str) -> List[str]:
        """Extract variables/entities from logical statements"""
        words = word_tokenize(sentence)
        tagged = pos_tag(words)
        
        # Extract nouns and proper nouns as potential variables
        variables = [word for word, pos in tagged if pos.startswith('NN') or pos.startswith('PRP')]
        return list(set(variables))  # Remove duplicates
    
    def format_for_pyke(self, assertions: List[LogicAssertion]) -> List[str]:
        """Convert assertions to PyKe-compatible format"""
        pyke_facts = []
        
        for assertion in assertions:
            if assertion.type == 'implication':
                # Convert to PyKe implication format
                pyke_fact = self._format_implication(assertion)
            elif assertion.type == 'conjunction':
                pyke_fact = self._format_conjunction(assertion)
            elif assertion.type == 'universal':
                pyke_fact = self._format_universal(assertion)
            else:
                # Generic format for other types
                pyke_fact = f"fact({assertion.type}, '{assertion.statement}')"
            
            if pyke_fact:
                pyke_facts.append(pyke_fact)
        
        return pyke_facts
    
    def _format_implication(self, assertion: LogicAssertion) -> str:
        """Format implication for PyKe"""
        # Simplified implication formatting
        variables = ', '.join(assertion.variables[:3])  # Limit variables
        return f"implies({variables}, '{assertion.statement}')"
    
    def _format_conjunction(self, assertion: LogicAssertion) -> str:
        """Format conjunction for PyKe"""
        variables = ', '.join(assertion.variables[:3])
        return f"and_fact({variables}, '{assertion.statement}')"
    
    def _format_universal(self, assertion: LogicAssertion) -> str:
        """Format universal quantification for PyKe"""
        if assertion.variables:
            var = assertion.variables[0]
            return f"forall({var}, '{assertion.statement}')"
        return f"universal_fact('{assertion.statement}')"
    
    def validate_structure(self, assertions: List[LogicAssertion]) -> bool:
        """Check if assertions are well-formed"""
        if not assertions:
            return False
        
        # Check for minimum confidence threshold
        avg_confidence = sum(a.confidence for a in assertions) / len(assertions)
        if avg_confidence < 0.5:
            return False
        
        # Check for variety in assertion types
        types = set(a.type for a in assertions)
        if len(types) == 1 and len(assertions) > 3:
            return False  # Suspicious if all assertions are the same type
        
        return True

# pyke_processor.py
import os
import tempfile
import subprocess
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class PyKeProcessor:
    def __init__(self, rules_dir: str = "./rules"):
        self.rules_dir = rules_dir
        self.engine = None
        self._ensure_rules_directory()
    
    def _ensure_rules_directory(self):
        """Ensure rules directory exists and has basic rules"""
        os.makedirs(self.rules_dir, exist_ok=True)
        
        # Create basic logic rules if they don't exist
        basic_rules_file = os.path.join(self.rules_dir, "basic_logic.krb")
        if not os.path.exists(basic_rules_file):
            self._create_basic_rules()
    
    def _create_basic_rules(self):
        """Create basic logical validation rules"""
        basic_rules = """
# basic_logic.krb - Basic logical validation rules

# Consistency rules
consistency_check
    use consistency_rule($fact1, $fact2)
    when
        implies($x, $y)
        not_fact($y)
        fact($x)
    assert
        contradiction($fact1, $fact2)

# Transitivity rule
transitivity
    use transitivity_rule($x, $y, $z)
    when
        implies($x, $y)
        implies($y, $z)
    assert
        implies($x, $z)

# Modus ponens
modus_ponens
    use mp_rule($x, $y)
    when
        implies($x, $y)
        fact($x)
    assert
        fact($y)

# Universal instantiation
universal_instantiation
    use ui_rule($x, $property)
    when
        forall($var, $property)
        instance($x, $var)
    assert
        has_property($x, $property)
"""
        
        with open(os.path.join(self.rules_dir, "basic_logic.krb"), 'w') as f:
            f.write(basic_rules)
        
        # Create corresponding facts file
        facts_file = """
# basic_logic.kfb - Basic facts for logic validation

# Define some basic instances for testing
instance(socrates, human)
instance(aristotle, human)
forall(human, mortal)
"""
        
        with open(os.path.join(self.rules_dir, "basic_logic.kfb"), 'w') as f:
            f.write(facts_file)
    
    def load_rules(self, rule_files: List[str] = None) -> bool:
        """Load PyKe rules from files"""
        try:
            # For this implementation, we'll simulate PyKe processing
            # In a real implementation, you would use the actual PyKe engine
            logger.info(f"Loading rules from {self.rules_dir}")
            return True
        except Exception as e:
            logger.error(f"Failed to load PyKe rules: {e}")
            return False
    
    def evaluate_logic(self, pyke_facts: List[str]) -> ValidationResult:
        """Evaluate logical assertions using PyKe engine"""
        try:
            # Create temporary facts file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.kfb', delete=False) as temp_file:
                for fact in pyke_facts:
                    temp_file.write(f"{fact}\n")
                temp_file_path = temp_file.name
            
            # Simulate PyKe validation
            validation_result = self._simulate_pyke_validation(pyke_facts)
            
            # Clean up
            os.unlink(temp_file_path)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"PyKe evaluation error: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[f"PyKe evaluation failed: {str(e)}"],
                warnings=[],
                pyke_output=None
            )
    
    def _simulate_pyke_validation(self, pyke_facts: List[str]) -> ValidationResult:
        """Simulate PyKe validation logic"""
        errors = []
        warnings = []
        
        # Basic logical consistency checks
        implications = [fact for fact in pyke_facts if 'implies' in fact]
        universals = [fact for fact in pyke_facts if 'forall' in fact]
        
        # Check for contradictions
        if self._check_contradictions(pyke_facts):
            errors.append("Logical contradiction detected in assertions")
        
        # Check for circular reasoning
        if self._check_circular_reasoning(implications):
            warnings.append("Potential circular reasoning detected")
        
        # Check for unsupported conclusions
        unsupported = self._check_unsupported_conclusions(pyke_facts)
        if unsupported:
            warnings.extend(unsupported)
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            pyke_output={
                "facts_processed": len(pyke_facts),
                "implications_found": len(implications),
                "universals_found": len(universals)
            }
        )
    
    def _check_contradictions(self, facts: List[str]) -> bool:
        """Check for logical contradictions"""
        # Simplified contradiction detection
        positive_facts = set()
        negative_facts = set()
        
        for fact in facts:
            if 'not_fact' in fact:
                # Extract the negated fact
                content = fact.split("'")[1] if "'" in fact else fact
                negative_facts.add(content.lower())
            else:
                content = fact.split("'")[1] if "'" in fact else fact
                positive_facts.add(content.lower())
        
        # Check for direct contradictions
        return bool(positive_facts.intersection(negative_facts))
    
    def _check_circular_reasoning(self, implications: List[str]) -> bool:
        """Check for circular reasoning in implications"""
        # Simplified circular reasoning detection
        if len(implications) < 2:
            return False
        
        # Look for patterns like A->B, B->A
        implication_pairs = []
        for impl in implications:
            # Extract variables (simplified)
            if 'implies(' in impl:
                content = impl.split('implies(')[1].split(')')[0]
                vars_part = content.split(',')[0] if ',' in content else content
                implication_pairs.append(vars_part.strip())
        
        # Check for simple circular patterns
        for i, pair1 in enumerate(implication_pairs):
            for j, pair2 in enumerate(implication_pairs[i+1:], i+1):
                if pair1 in pair2 and pair2 in pair1:
                    return True
        
        return False
    
    def _check_unsupported_conclusions(self, facts: List[str]) -> List[str]:
        """Check for conclusions without sufficient support"""
        warnings = []
        
        # Count different types of logical structures
        implications = len([f for f in facts if 'implies' in f])
        universals = len([f for f in facts if 'forall' in f])
        basic_facts = len([f for f in facts if 'fact(' in f])
        
        # Warn if there are many conclusions but few supporting facts
        if implications > basic_facts * 2:
            warnings.append("High ratio of implications to supporting facts")
        
        if universals > 0 and basic_facts == 0:
            warnings.append("Universal statements without supporting instances")
        
        return warnings

# refinement_generator.py
from typing import List
import logging

logger = logging.getLogger(__name__)

class RefinementGenerator:
    def __init__(self):
        self.error_templates = {
            'contradiction': "Logical contradiction detected: {detail}",
            'circular_reasoning': "Circular reasoning found: {detail}",
            'unsupported_conclusion': "Conclusion lacks sufficient support: {detail}",
            'invalid_inference': "Invalid logical inference: {detail}"
        }
        
        self.suggestion_templates = {
            'contradiction': "Remove or modify conflicting statements to ensure consistency",
            'circular_reasoning': "Restructure arguments to avoid circular dependencies",
            'unsupported_conclusion': "Provide additional premises or evidence to support conclusions",
            'invalid_inference': "Verify logical connections and strengthen reasoning steps"
        }
    
    def analyze_validation_errors(self, validation_result: ValidationResult) -> List[Dict[str, Any]]:
        """Analyze validation errors and categorize them"""
        analyzed_errors = []
        
        for error in validation_result.errors:
            error_type = self._categorize_error(error)
            analyzed_errors.append({
                'type': error_type,
                'message': error,
                'severity': 'high'
            })
        
        for warning in validation_result.warnings:
            warning_type = self._categorize_error(warning)
            analyzed_errors.append({
                'type': warning_type,
                'message': warning,
                'severity': 'medium'
            })
        
        return analyzed_errors
    
    def _categorize_error(self, error_message: str) -> str:
        """Categorize error based on message content"""
        error_lower = error_message.lower()
        
        if 'contradiction' in error_lower:
            return 'contradiction'
        elif 'circular' in error_lower:
            return 'circular_reasoning'
        elif 'unsupported' in error_lower or 'support' in error_lower:
            return 'unsupported_conclusion'
        elif 'inference' in error_lower or 'reasoning' in error_lower:
            return 'invalid_inference'
        else:
            return 'general_logic_error'
    
    def generate_correction_suggestions(self, analyzed_errors: List[Dict[str, Any]]) -> List[RefinementProposal]:
        """Generate specific correction suggestions"""
        proposals = []
        
        for error in analyzed_errors:
            error_type = error['type']
            severity = error['severity']
            
            # Determine priority based on severity
            priority = 1 if severity == 'high' else 2 if severity == 'medium' else 3
            
            suggestion = self.suggestion_templates.get(
                error_type, 
                "Review and revise the logical structure of your response"
            )
            
            proposal = RefinementProposal(
                issue=error['message'],
                suggestion=suggestion,
                priority=priority
            )
            proposals.append(proposal)
        
        return proposals
    
    def format_refinement_prompt(self, suggestions: List[RefinementProposal]) -> str:
        """Format refinement suggestions into a coherent prompt"""
        if not suggestions:
            return "Please review your response for logical consistency."
        
        # Sort by priority
        sorted_suggestions = sorted(suggestions, key=lambda x: x.priority)
        
        prompt_parts = ["Please address the following logical issues:"]
        
        for i, suggestion in enumerate(sorted_suggestions, 1):
            priority_label = ["HIGH", "MEDIUM", "LOW"][suggestion.priority - 1]
            prompt_parts.append(
                f"{i}. [{priority_label}] {suggestion.issue}\n"
                f"   Suggestion: {suggestion.suggestion}"
            )
        
        prompt_parts.append(
            "\nPlease revise your response to address these issues while "
            "maintaining the core information and improving logical consistency."
        )
        
        return "\n\n".join(prompt_parts)

# main_api.py
import asyncio
import time
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import logging
from contextlib import asynccontextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LogicValidatorAPI:
    def __init__(self):
        self.config = Config()
        self.llm_interface = LLMInterface(
            api_key=self.config.OPENAI_API_KEY,
            model=self.config.LLM_MODEL,
            temperature=self.config.LLM_TEMPERATURE
        )
        self.logic_validator = LogicValidator()
        self.pyke_processor = PyKeProcessor(self.config.PYKE_RULES_DIR)
        self.refinement_generator = RefinementGenerator()
        
        # Initialize PyKe rules
        self.pyke_processor.load_rules()
    
    async def process_query(self, query: UserQuery) -> FinalResponse:
        """Main processing pipeline for logic validation"""
        start_time = time.time()
        refinement_iterations = 0
        
        try:
            # Step 1: Generate initial LLM response
            logger.info(f"Processing query: {query.text[:50]}...")
            llm_response = await self.llm_interface.generate_response(
                query.text, query.context
            )
            
            # Step 2: Start validation loop
            current_response = llm_response
            all_refinement_proposals = []
            
            for iteration in range(self.config.MAX_REFINEMENT_ITERATIONS):
                refinement_iterations = iteration + 1
                
                # Extract logic assertions
                assertions = self.logic_validator.extract_logic_assertions(current_response)
                
                if not assertions:
                    logger.warning("No logical assertions found in response")
                    break
                
                # Validate structure
                if not self.logic_validator.validate_structure(assertions):
                    logger.warning("Assertions failed structure validation")
                    break
                
                # Convert to PyKe format
                pyke_facts = self.logic_validator.format_for_pyke(assertions)
                
                # Validate with PyKe
                validation_result = self.pyke_processor.evaluate_logic(pyke_facts)
                
                if validation_result.is_valid:
                    # Logic is valid, exit loop
                    logger.info(f"Logic validated successfully after {iteration + 1} iteration(s)")
                    break
                
                # Generate refinement proposals
                analyzed_errors = self.refinement_generator.analyze_validation_errors(validation_result)
                refinement_proposals = self.refinement_generator.generate_correction_suggestions(analyzed_errors)
                all_refinement_proposals.extend(refinement_proposals)
                
                # Refine response
                if iteration < self.config.MAX_REFINEMENT_ITERATIONS - 1:
                    current_response = await self.llm_interface.refine_response(
                        current_response, refinement_proposals
                    )
                    logger.info(f"Refined response (iteration {iteration + 1})")
                else:
                    logger.warning("Maximum refinement iterations reached")
            
            # Final validation
            final_assertions = self.logic_validator.extract_logic_assertions(current_response)
            final_pyke_facts = self.logic_validator.format_for_pyke(final_assertions)
            final_validation = self.pyke_processor.evaluate_logic(final_pyke_facts)
            
            # Determine final status
            if final_validation.is_valid:
                status = ValidationStatus.VALID
            elif refinement_iterations >= self.config.MAX_REFINEMENT_ITERATIONS:
                status = ValidationStatus.NEEDS_REFINEMENT
            else:
                status = ValidationStatus.INVALID
            
            processing_time = time.time() - start_time
            
            return FinalResponse(
                original_query=query.text,
                llm_response=llm_response,
                validation_status=status,
                assertions_extracted=final_assertions,
                validation_result=final_validation,
                refinement_iterations=refinement_iterations,
                refinement_proposals=all_refinement_proposals,
                final_validated_response=current_response,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            processing_time = time.time() - start_time
            
            return FinalResponse(
                original_query=query.text,
                llm_response="Error occurred during processing",
                validation_status=ValidationStatus.ERROR,
                assertions_extracted=[],
                validation_result=ValidationResult(
                    is_valid=False,
                    errors=[str(e)],
                    warnings=[],
                    pyke_output=None
                ),
                refinement_iterations=0,
                refinement_proposals=[],
                final_validated_response="Processing failed",
                processing_time=processing_time
            )

# FastAPI application
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting Logic Validator API")
    yield
    # Shutdown
    logger.info("Shutting down Logic Validator API")

app = FastAPI(
    title="LLM Logic Validator API",
    description="API for validating logical consistency in LLM responses using PyKe",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize API controller
api_controller = LogicValidatorAPI()

@app.post("/validate-logic", response_model=FinalResponse)
async def validate_logic_response(query: UserQuery):
    """Main endpoint for logic validation"""
    try:
        result = await api_controller.process_query(query)
        return result
    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/extract-logic")
async def extract_logic(text: str):
    """Extract logical assertions from text"""
    try:
        assertions = api_controller.logic_validator.extract_logic_assertions(text)
        return {"assertions": assertions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/validate-assertions")
async def validate_assertions(assertions: List[LogicAssertion]):
    """Validate logical assertions directly"""
    try:
        pyke_facts = api_controller.logic_validator.format_for_pyke(assertions)
        validation_result = api_controller.pyke_processor.evaluate_logic(pyke_facts)
        return validation_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/rules")
async def list_rules():
    """List available logic rules"""
    try:
        rules_dir = api_controller.config.PYKE_RULES_DIR
        rule_files = [f for f in os.listdir(rules_dir) if f.endswith('.krb')]
        return {"rules_directory": rules_dir, "rule_files": rule_files}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.datetime.now()}

if __name__ == "__main__":
    import uvicorn
    config = Config()
    uvicorn.run(
        "main_api:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=True
    )

# test_api.py
import pytest
import asyncio
from fastapi.testclient import TestClient
from main_api import app

client = TestClient(app)

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_extract_logic():
    test_text = "If it rains, then the ground gets wet. All humans are mortal."
    response = client.post("/extract-logic", params={"text": test_text})
    assert response.status_code == 200
    assertions = response.json()["assertions"]
    assert len(assertions) > 0

def test_validate_logic_response():
    query = {
        "text": "All birds can fly. Penguins are birds. Therefore, penguins can fly.",
        "context": "Logical reasoning test",
        "validation_strictness": 0.8
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()
    assert "validation_status" in result
    assert "assertions_extracted" in result

def test_list_rules():
    response = client.get("/rules")
    assert response.status_code == 200
    rules_info = response.json()
    assert "rules_directory" in rules_info
    assert "rule_files" in rules_info

# Example usage script
async def example_usage():
    """Example of how to use the API"""
    
    # Initialize the API controller
    api = LogicValidatorAPI()
    
    # Example query with logical issues
    query = UserQuery(
        text="All birds can fly. Penguins are birds. Therefore, penguins can fly.",
        context="Testing logical consistency",
        validation_strictness=0.8
    )
    
    # Process the query
    result = await api.process_query(query)
    
    print(f"Original Query: {result.original_query}")
    print(f"LLM Response: {result.llm_response}")
    print(f"Validation Status: {result.validation_status}")
    print(f"Assertions Found: {len(result.assertions_extracted)}")
    print(f"Refinement Iterations: {result.refinement_iterations}")
    print(f"Processing Time: {result.processing_time:.2f}s")
    
    if result.refinement_proposals:
        print("\nRefinement Proposals:")
        for proposal in result.refinement_proposals:
            print(f"- {proposal.issue}")
            print(f"  Suggestion: {proposal.suggestion}")

if __name__ == "__main__":
    # Run example
    asyncio.run(example_usage())
```

This comprehensive implementation provides:

## Key Features

1. **Complete Architecture**: All components from the flowchart are implemented with proper separation of concerns

2. **LLM Integration**: Flexible interface supporting OpenAI (easily adaptable to other providers)

3. **Logic Extraction**: Advanced pattern matching and NLP-based extraction of logical assertions

4. **PyKe Integration**: Simulated PyKe engine with extensible rule system

5. **Refinement Loop**: Intelligent refinement with priority-based suggestions

6. **FastAPI Backend**: Production-ready REST API with comprehensive endpoints

7. **Error Handling**: Robust error handling and validation throughout

8. **Testing**: Included test suite for all major components

## Usage

1. **Install dependencies**: `pip install -r requirements.txt`

2. **Set environment variables**:
   ```bash
   export OPENAI_API_KEY="your-key-here"
   export PYKE_RULES_DIR="./rules"
   ```

3. **Run the API**: `python main_api.py`

4. **Test the system**:
   ```bash
   curl -X POST "http://localhost:8000/validate-logic" \
        -H "Content-Type: application/json" \
        -d '{"text": "All birds fly. Penguins are birds. So penguins fly."}'
   ```

The system will detect the logical inconsistency and suggest refinements to create a more logically sound response.