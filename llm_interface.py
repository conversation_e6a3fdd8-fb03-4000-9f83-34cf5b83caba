import openai
import asyncio
import os
from typing import List, Optional
import logging
from dotenv import load_dotenv
from models import RefinementProposal

logger = logging.getLogger(__name__)

class LLMInterface:
    def __init__(self):
        """Initialize with configuration from environment variables"""
        load_dotenv()  # Load environment variables from .env
        
        self.api_key = os.getenv("LLM_API_KEY")
        self.model = os.getenv("LLM_MODEL_NAME", "gpt-4")
        self.temperature = float(os.getenv("LLM_TEMPERATURE", "0.3"))
        self.endpoint = os.getenv("LLM_API_ENDPOINT", "https://api.openai.com/v1")
        
        self.client = openai.AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.endpoint
        )
    
    async def generate_response(self, user_input: str, context: Optional[str] = None) -> str:
        """Generate initial response from LLM"""
        try:
            system_prompt = """You are a logical reasoning assistant. Provide clear, 
            logically structured responses. Use explicit logical connections and 
            reasoning steps in your answers."""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Context: {context}\n\nQuery: {user_input}" if context else user_input}
            ]
            
            # Add error handling for missing API key
            if not self.api_key:
                raise ValueError("LLM_API_KEY is not set in environment variables")
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=1500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            raise Exception(f"Failed to generate LLM response: {e}")
    
    async def refine_response(self, original_response: str, refinement_proposals: List[RefinementProposal], query_text: str = None) -> str:
        """Refine LLM response based on logical validation feedback with enhanced prompting"""
        try:
            # Create enhanced refinement prompt
            from refinement_generator import RefinementGenerator
            refinement_gen = RefinementGenerator()
            refinement_prompt_text = refinement_gen.format_refinement_prompt(refinement_proposals, query_text)

            refinement_prompt = f"""
            Please revise the following response to address the identified logical issues:

            Original Response:
            {original_response}

            {refinement_prompt_text}

            Important: Maintain the core information while ensuring logical consistency and validity.
            """

            system_prompt = """You are a logical reasoning expert specializing in creating logically sound and well-structured responses.
            Your task is to revise responses to eliminate logical errors while preserving the essential information and improving clarity."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": refinement_prompt}
            ]

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature * 0.7,  # Lower temperature for more focused refinement
                max_tokens=2000  # Increased token limit for more comprehensive refinements
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"LLM refinement error: {e}")
            raise Exception(f"Failed to refine LLM response: {e}")
