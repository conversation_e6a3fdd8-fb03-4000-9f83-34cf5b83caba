import re
import nltk
from typing import List, Dict, Tuple
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.tag import pos_tag
from models import LogicAssertion

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

class LogicValidator:
    def __init__(self):
        # Enhanced logic patterns for extraction
        self.logic_patterns = {
            'implication': [
                r'if\s+(.+?)\s+then\s+(.+?)(?:\.|$)',
                r'(.+?)\s+implies\s+(.+?)(?:\.|$)',
                r'when\s+(.+?),\s*(.+?)(?:\.|$)',
                r'(.+?)\s+leads\s+to\s+(.+?)(?:\.|$)',
                r'(.+?)\s+results\s+in\s+(.+?)(?:\.|$)',
                r'given\s+(.+?),\s*(.+?)(?:\.|$)',
                r'assuming\s+(.+?),\s*(.+?)(?:\.|$)'
            ],
            'conjunction': [
                r'(.+?)\s+and\s+(.+?)(?:\.|$)',
                r'both\s+(.+?)\s+and\s+(.+?)(?:\.|$)',
                r'(.+?)\s+as\s+well\s+as\s+(.+?)(?:\.|$)',
                r'(.+?)\s+plus\s+(.+?)(?:\.|$)'
            ],
            'disjunction': [
                r'(.+?)\s+or\s+(.+?)(?:\.|$)',
                r'either\s+(.+?)\s+or\s+(.+?)(?:\.|$)',
                r'(.+?)\s+alternatively\s+(.+?)(?:\.|$)'
            ],
            'negation': [
                r'not\s+(.+?)(?:\.|$)',
                r'it\s+is\s+not\s+the\s+case\s+that\s+(.+?)(?:\.|$)',
                r'(.+?)\s+is\s+false(?:\.|$)',
                r'(.+?)\s+cannot\s+(.+?)(?:\.|$)',
                r'(.+?)\s+does\s+not\s+(.+?)(?:\.|$)'
            ],
            'universal': [
                r'all\s+(.+?)\s+are\s+(.+?)(?:\.|$)',
                r'every\s+(.+?)\s+is\s+(.+?)(?:\.|$)',
                r'each\s+(.+?)\s+(.+?)(?:\.|$)',
                r'any\s+(.+?)\s+(.+?)(?:\.|$)',
                r'(.+?)\s+always\s+(.+?)(?:\.|$)'
            ],
            'existential': [
                r'some\s+(.+?)\s+are\s+(.+?)(?:\.|$)',
                r'there\s+exists?\s+(.+?)(?:\.|$)',
                r'at\s+least\s+one\s+(.+?)(?:\.|$)',
                r'(.+?)\s+sometimes\s+(.+?)(?:\.|$)'
            ],
            'causal': [
                r'(.+?)\s+causes\s+(.+?)(?:\.|$)',
                r'(.+?)\s+because\s+(.+?)(?:\.|$)',
                r'(.+?)\s+due\s+to\s+(.+?)(?:\.|$)',
                r'(.+?)\s+since\s+(.+?)(?:\.|$)'
            ],
            'conditional': [
                r'unless\s+(.+?),\s*(.+?)(?:\.|$)',
                r'provided\s+that\s+(.+?),\s*(.+?)(?:\.|$)',
                r'in\s+case\s+(.+?),\s*(.+?)(?:\.|$)'
            ]
        }

        # Domain-specific patterns
        self.domain_patterns = {
            'mathematics': [
                r'(.+?)\s+equals\s+(.+?)(?:\.|$)',
                r'(.+?)\s+is\s+greater\s+than\s+(.+?)(?:\.|$)',
                r'(.+?)\s+is\s+less\s+than\s+(.+?)(?:\.|$)',
                r'(.+?)\s+proves\s+(.+?)(?:\.|$)'
            ],
            'science': [
                r'(.+?)\s+hypothesis\s+(.+?)(?:\.|$)',
                r'(.+?)\s+evidence\s+(.+?)(?:\.|$)',
                r'(.+?)\s+observation\s+(.+?)(?:\.|$)'
            ]
        }
    
    def extract_logic_assertions(self, llm_response: str, query_text: str = None) -> List[LogicAssertion]:
        """Extract logical assertions from LLM response with domain awareness"""
        assertions = []
        sentences = sent_tokenize(llm_response)

        # Detect domain for domain-specific patterns
        domain = self._detect_domain(query_text) if query_text else None

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # Skip very short sentences
                continue

            # Check standard logic patterns
            pattern_matched = False
            for logic_type, patterns in self.logic_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, sentence, re.IGNORECASE)
                    for _ in matches:  # Just need to know if there's a match
                        assertion = LogicAssertion(
                            statement=sentence,
                            type=logic_type,
                            confidence=self._calculate_confidence(sentence, logic_type),
                            variables=self._extract_variables(sentence)
                        )
                        assertions.append(assertion)
                        pattern_matched = True
                        break
                if pattern_matched:
                    break

            # If no standard pattern matched and we have a domain, check domain patterns
            if not pattern_matched and domain and domain in self.domain_patterns:
                for pattern in self.domain_patterns[domain]:
                    matches = re.finditer(pattern, sentence, re.IGNORECASE)
                    for _ in matches:
                        assertion = LogicAssertion(
                            statement=sentence,
                            type=f"{domain}_specific",
                            confidence=self._calculate_confidence(sentence, f"{domain}_specific"),
                            variables=self._extract_variables(sentence)
                        )
                        assertions.append(assertion)
                        break

        return assertions

    def _detect_domain(self, query_text: str) -> str:
        """Detect the domain of the query for specialized pattern matching"""
        if not query_text:
            return None

        query_lower = query_text.lower()

        # Mathematics keywords
        math_keywords = ['equation', 'solve', 'calculate', 'number', 'algebra', 'geometry',
                        'theorem', 'proof', 'derivative', 'integral', 'function']

        # Science keywords
        science_keywords = ['experiment', 'hypothesis', 'theory', 'observation', 'data',
                           'analysis', 'scientific', 'research', 'study', 'evidence']

        # Count keyword matches
        math_score = sum(1 for keyword in math_keywords if keyword in query_lower)
        science_score = sum(1 for keyword in science_keywords if keyword in query_lower)

        # Determine domain based on highest score
        if math_score > science_score and math_score > 0:
            return 'mathematics'
        elif science_score > 0:
            return 'science'

        return None
    
    def _calculate_confidence(self, sentence: str, logic_type: str) -> float:
        """Calculate confidence score for logic extraction"""
        # Basic confidence calculation based on pattern strength and sentence structure
        base_confidence = 0.7
        
        # Boost confidence for clear logical indicators
        logical_indicators = ['therefore', 'thus', 'hence', 'consequently', 'because']
        if any(indicator in sentence.lower() for indicator in logical_indicators):
            base_confidence += 0.2
        
        # Reduce confidence for complex sentences
        if len(sentence.split()) > 20:
            base_confidence -= 0.1
        
        return min(max(base_confidence, 0.0), 1.0)
    
    def _extract_variables(self, sentence: str) -> List[str]:
        """Extract variables/entities from logical statements"""
        words = word_tokenize(sentence)
        tagged = pos_tag(words)
        
        # Extract nouns and proper nouns as potential variables
        variables = [word for word, pos in tagged if pos.startswith('NN') or pos.startswith('PRP')]
        return list(set(variables))  # Remove duplicates
    
    def format_for_pyke(self, assertions: List[LogicAssertion]) -> List[str]:
        """Convert assertions to PyKe-compatible format with enhanced type handling"""
        pyke_facts = []

        for assertion in assertions:
            if assertion.type == 'implication':
                pyke_fact = self._format_implication(assertion)
            elif assertion.type == 'conjunction':
                pyke_fact = self._format_conjunction(assertion)
            elif assertion.type == 'universal':
                pyke_fact = self._format_universal(assertion)
            elif assertion.type == 'existential':
                pyke_fact = self._format_existential(assertion)
            elif assertion.type == 'negation':
                pyke_fact = self._format_negation(assertion)
            elif assertion.type == 'causal':
                pyke_fact = self._format_causal(assertion)
            elif assertion.type == 'conditional':
                pyke_fact = self._format_conditional(assertion)
            elif assertion.type.endswith('_specific'):
                pyke_fact = self._format_domain_specific(assertion)
            else:
                # Generic format for other types
                pyke_fact = f"fact({assertion.type}, '{assertion.statement}')"

            if pyke_fact:
                pyke_facts.append(pyke_fact)

        return pyke_facts
    
    def _format_implication(self, assertion: LogicAssertion) -> str:
        """Format implication for PyKe"""
        # Simplified implication formatting
        variables = ', '.join(assertion.variables[:3])  # Limit variables
        return f"implies({variables}, '{assertion.statement}')"
    
    def _format_conjunction(self, assertion: LogicAssertion) -> str:
        """Format conjunction for PyKe"""
        variables = ', '.join(assertion.variables[:3])
        return f"and_fact({variables}, '{assertion.statement}')"
    
    def _format_universal(self, assertion: LogicAssertion) -> str:
        """Format universal quantification for PyKe"""
        if assertion.variables:
            var = assertion.variables[0]
            return f"forall({var}, '{assertion.statement}')"
        return f"universal_fact('{assertion.statement}')"

    def _format_existential(self, assertion: LogicAssertion) -> str:
        """Format existential quantification for PyKe"""
        if assertion.variables:
            var = assertion.variables[0]
            return f"exists({var}, '{assertion.statement}')"
        return f"existential_fact('{assertion.statement}')"

    def _format_negation(self, assertion: LogicAssertion) -> str:
        """Format negation for PyKe"""
        variables = ', '.join(assertion.variables[:2])
        return f"not_fact({variables}, '{assertion.statement}')"

    def _format_causal(self, assertion: LogicAssertion) -> str:
        """Format causal relationships for PyKe"""
        variables = ', '.join(assertion.variables[:2])
        return f"causes({variables}, '{assertion.statement}')"

    def _format_conditional(self, assertion: LogicAssertion) -> str:
        """Format conditional statements for PyKe"""
        variables = ', '.join(assertion.variables[:2])
        return f"conditional({variables}, '{assertion.statement}')"

    def _format_domain_specific(self, assertion: LogicAssertion) -> str:
        """Format domain-specific assertions for PyKe"""
        domain = assertion.type.replace('_specific', '')
        variables = ', '.join(assertion.variables[:3])
        return f"{domain}_fact({variables}, '{assertion.statement}')"
    
    def validate_structure(self, assertions: List[LogicAssertion]) -> bool:
        """Check if assertions are well-formed"""
        if not assertions:
            return False
        
        # Check for minimum confidence threshold
        avg_confidence = sum(a.confidence for a in assertions) / len(assertions)
        if avg_confidence < 0.5:
            return False
        
        # Check for variety in assertion types
        types = set(a.type for a in assertions)
        if len(types) == 1 and len(assertions) > 3:
            return False  # Suspicious if all assertions are the same type
        
        return True
