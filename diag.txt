flowchart TD
    UserInput["User Input"] -->|Submit Query| LLM["LLM Generates Initial Response"]
    LLM -->|Extract Logic/Assertions| LogicValidator["Logic Validator"]
    LogicValidator -->|Pass to| PyKe["PyKe Logic Engine"]
    PyKe -->|Evaluate Logic/Rules| ValidationResult{"Logic Valid?"}
    ValidationResult -- Yes --> FinalResponse["Final LLM Response"]
    ValidationResult -- No --> Refiner["Refinement Proposal Generator"]
    Refiner -->|Suggest Correction| LLM
    LLM -->|Regenerate/Adjust| LogicValidator
