import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from main_api import app
from models import UserQuery, LogicAssertion

client = TestClient(app)

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_extract_logic():
    test_text = "If it rains, then the ground gets wet. All humans are mortal."
    response = client.post("/extract-logic", params={"text": test_text})
    assert response.status_code == 200
    assertions = response.json()["assertions"]
    assert len(assertions) > 0

def test_validate_logic_response():
    query = {
        "text": "All birds can fly. Penguins are birds. Therefore, penguins can fly.",
        "context": "Logical reasoning test",
        "validation_strictness": 0.8
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()
    assert "validation_status" in result
    assert "assertions_extracted" in result

def test_list_rules():
    response = client.get("/rules")
    assert response.status_code == 200
    rules_info = response.json()
    assert "rules_directory" in rules_info
    assert "rule_files" in rules_info

# Comprehensive workflow tests
def test_mathematics_domain_workflow():
    """Test the complete workflow with a mathematics query"""
    query = {
        "text": "If x = 2 and y = 3, then x + y = 5. Since x + y = 5, we can conclude that 2 + 3 = 5.",
        "context": "Mathematical reasoning test",
        "validation_strictness": 0.9
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()

    # Check that domain-specific rules were applied
    assert "validation_status" in result
    assert "assertions_extracted" in result
    assert len(result["assertions_extracted"]) > 0

    # Check for mathematical reasoning patterns
    assertions = result["assertions_extracted"]
    math_assertions = [a for a in assertions if 'mathematics' in a.get('type', '')]
    # Should detect mathematical patterns

def test_science_domain_workflow():
    """Test the complete workflow with a science query"""
    query = {
        "text": "Based on experimental observations, increased temperature causes faster molecular motion. Therefore, heating a gas will increase its pressure.",
        "context": "Scientific reasoning test",
        "validation_strictness": 0.8
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()

    assert "validation_status" in result
    assert "assertions_extracted" in result
    # Should detect causal relationships and scientific reasoning

def test_logic_domain_workflow():
    """Test the complete workflow with formal logic"""
    query = {
        "text": "If all humans are mortal, and Socrates is human, then Socrates is mortal. This follows from universal instantiation.",
        "context": "Formal logic test",
        "validation_strictness": 0.9
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()

    assert "validation_status" in result
    assert result["validation_status"] in ["valid", "needs_refinement"]
    # Should validate formal logical structure

def test_refinement_loop_workflow():
    """Test that the refinement loop works as per flowchart"""
    query = {
        "text": "All birds can fly. Penguins are birds. Therefore, penguins can fly.",
        "context": "Testing refinement loop",
        "validation_strictness": 0.8
    }
    response = client.post("/validate-logic", json=query)
    assert response.status_code == 200
    result = response.json()

    # Should detect logical issues and attempt refinement
    assert "refinement_iterations" in result
    assert "refinement_proposals" in result

    # If refinement occurred, should have proposals
    if result["refinement_iterations"] > 0:
        assert len(result["refinement_proposals"]) > 0

# Example usage script with enhanced testing
async def comprehensive_workflow_test():
    """Comprehensive test of the complete workflow as described in diag.txt"""

    # Initialize the API controller
    from main_api import LogicValidatorAPI
    api = LogicValidatorAPI()

    test_cases = [
        {
            "name": "Mathematics Test",
            "query": UserQuery(
                text="If f(x) = x^2 and f(3) = 9, then the derivative f'(3) = 6.",
                context="Mathematical function analysis",
                validation_strictness=0.9
            )
        },
        {
            "name": "Science Test",
            "query": UserQuery(
                text="The hypothesis that plants grow faster with more sunlight is supported by controlled experiments showing increased growth rates.",
                context="Scientific hypothesis testing",
                validation_strictness=0.8
            )
        },
        {
            "name": "Logic Test",
            "query": UserQuery(
                text="If all mammals are warm-blooded, and whales are mammals, then whales are warm-blooded.",
                context="Formal logical reasoning",
                validation_strictness=0.9
            )
        },
        {
            "name": "Problematic Logic Test",
            "query": UserQuery(
                text="All birds can fly. Penguins are birds. Therefore, penguins can fly.",
                context="Testing error detection and refinement",
                validation_strictness=0.8
            )
        }
    ]

    print("=== Comprehensive Workflow Test ===\n")

    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"Query: {test_case['query'].text}")

        # Process the query through the complete workflow
        result = await api.process_query(test_case['query'])

        print(f"Validation Status: {result.validation_status}")
        print(f"Assertions Found: {len(result.assertions_extracted)}")
        print(f"Refinement Iterations: {result.refinement_iterations}")
        print(f"Processing Time: {result.processing_time:.2f}s")

        # Show domain detection
        if result.validation_result.pyke_output:
            domain = result.validation_result.pyke_output.get('domain_detected', 'unknown')
            rules = result.validation_result.pyke_output.get('rules_applied', [])
            print(f"Domain Detected: {domain}")
            print(f"Rules Applied: {', '.join(rules)}")

        if result.refinement_proposals:
            print("Refinement Proposals:")
            for proposal in result.refinement_proposals:
                print(f"  - {proposal.issue}")
                print(f"    Suggestion: {proposal.suggestion}")

        print(f"Final Response Length: {len(result.final_validated_response)} characters")
        print("-" * 60)

if __name__ == "__main__":
    # Run comprehensive test
    asyncio.run(comprehensive_workflow_test())
