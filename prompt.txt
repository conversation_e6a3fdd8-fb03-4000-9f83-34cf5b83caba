You are a genius python engineer and software architect.

You are tasked with implementing a Python API that integrates Large Language Model (LLM) responses with logical validation using the PyKe logic engine. Follow the flowchart architecture below to create a robust system that ensures LLM outputs are logically sound.

## System Architecture Overview

The system follows this flow:
1. **User Input** → LLM generates initial response
2. **Logic Validator** extracts logical assertions from LLM output
3. **PyKe Engine** evaluates extracted logic against predefined rules
4. **Validation Decision** determines if logic is valid
5. **Response Path**: Valid logic → Final response | Invalid logic → Refinement loop

## Implementation Requirements

### Core Components to Implement

#### 1. LLM Interface Module
- Create a class `LLMInterface` that handles:
  - Initial response generation from user input
  - Response regeneration/adjustment based on refinement suggestions
  - Integration with your chosen LLM provider (OpenAI, Anthropic, etc.)

#### 2. Logic Validator Class
- Implement `LogicValidator` with methods:
  - `extract_logic_assertions(llm_response)`: Parse LLM output for logical statements
  - `format_for_pyke(assertions)`: Convert assertions to PyKe-compatible format
  - `validate_structure(assertions)`: Check if assertions are well-formed

#### 3. PyKe Logic Engine Integration
- Create `PyKeProcessor` class:
  - `load_rules(rule_files)`: Load predefined logical rules
  - `evaluate_logic(assertions)`: Run PyKe inference on extracted logic
  - `get_validation_result()`: Return boolean validation status with details

#### 4. Refinement Proposal Generator
- Implement `RefinementGenerator`:
  - `analyze_validation_errors(pyke_output)`: Identify specific logical issues
  - `generate_correction_suggestions(errors)`: Create actionable refinement proposals
  - `format_refinement_prompt(suggestions)`: Prepare input for LLM re-processing

#### 5. Main API Controller
- Create `LogicValidatorAPI` class that orchestrates the entire flow:
  - Manages the validation loop
  - Handles maximum retry limits
  - Provides clean API endpoints

## Technical Specifications

### Dependencies
```python
# Required packages
pyke3  # For logic engine
openai  # Or your preferred LLM client
fastapi  # For API endpoints
pydantic  # For data validation
asyncio  # For async operations
```

### Data Models
Define Pydantic models for:
- `UserQuery`: Input structure
- `LogicAssertion`: Extracted logical statements
- `ValidationResult`: PyKe evaluation output
- `RefinementProposal`: Correction suggestions
- `FinalResponse`: Complete API response

### Error Handling
Implement comprehensive error handling for:
- LLM API failures
- PyKe engine errors
- Malformed logic extraction
- Infinite refinement loops
- Timeout scenarios

### Configuration
Create configurable parameters for:
- Maximum refinement iterations (default: 3)
- PyKe rule file paths
- LLM model selection
- Validation strictness levels
- Response timeout limits

## API Endpoints

### Primary Endpoint
```python
@app.post("/validate-logic")
async def validate_logic_response(query: UserQuery) -> FinalResponse:
    # Main validation pipeline
```

### Supporting Endpoints
```python
@app.post("/extract-logic")  # For testing logic extraction
@app.post("/validate-assertions")  # For direct PyKe validation
@app.get("/rules")  # List available logic rules
```

## Testing Requirements

Create comprehensive tests for:
- Logic extraction accuracy
- PyKe rule evaluation
- Refinement loop termination
- Edge cases and error conditions
- Performance under load

## Implementation Notes

1. **Logic Extraction Strategy**: Use regex patterns, NLP libraries, or fine-tuned models to identify logical statements in LLM responses

2. **PyKe Integration**: Ensure proper fact and rule file formatting for PyKe engine compatibility

3. **Refinement Quality**: Design refinement suggestions to be specific and actionable for the LLM

4. **Performance**: Implement caching for repeated validations and async processing for better throughput

5. **Monitoring**: Add logging for validation success rates and common failure patterns


Begin implementation with the core validation loop, then expand to include error handling, optimization, and comprehensive testing.