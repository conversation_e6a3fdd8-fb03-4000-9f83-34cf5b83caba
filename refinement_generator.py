from typing import List, Dict, Any
import logging
from models import RefinementProposal, ValidationResult

logger = logging.getLogger(__name__)

class RefinementGenerator:
    def __init__(self):
        self.error_templates = {
            'contradiction': "Logical contradiction detected: {detail}",
            'circular_reasoning': "Circular reasoning found: {detail}",
            'unsupported_conclusion': "Conclusion lacks sufficient support: {detail}",
            'invalid_inference': "Invalid logical inference: {detail}",
            'rule_violation': "Rule violation detected: {detail}",
            'domain_specific': "Domain-specific issue: {detail}",
            'insufficient_evidence': "Insufficient evidence for claim: {detail}",
            'weak_connection': "Weak logical connection: {detail}"
        }

        self.suggestion_templates = {
            'contradiction': "Remove or modify conflicting statements to ensure consistency",
            'circular_reasoning': "Restructure arguments to avoid circular dependencies",
            'unsupported_conclusion': "Provide additional premises or evidence to support conclusions",
            'invalid_inference': "Verify logical connections and strengthen reasoning steps",
            'rule_violation': "Ensure reasoning follows established logical rules",
            'domain_specific': "Apply domain-specific reasoning principles",
            'insufficient_evidence': "Provide more evidence or qualify the strength of claims",
            'weak_connection': "Strengthen the logical connections between statements"
        }

        # Context-aware refinement strategies
        self.domain_strategies = {
            'mathematics': {
                'focus': 'Ensure mathematical rigor and proof validity',
                'suggestions': [
                    'Provide step-by-step mathematical reasoning',
                    'Verify all mathematical operations and logic',
                    'Include necessary assumptions and constraints'
                ]
            },
            'science': {
                'focus': 'Ensure scientific method and evidence-based reasoning',
                'suggestions': [
                    'Support claims with empirical evidence',
                    'Consider alternative hypotheses',
                    'Acknowledge limitations and uncertainties'
                ]
            },
            'logic': {
                'focus': 'Ensure formal logical validity',
                'suggestions': [
                    'Use valid logical forms and structures',
                    'Avoid logical fallacies',
                    'Ensure premises support conclusions'
                ]
            }
        }
    
    def analyze_validation_errors(self, validation_result: ValidationResult) -> List[Dict[str, Any]]:
        """Analyze validation errors and categorize them"""
        analyzed_errors = []
        
        for error in validation_result.errors:
            error_type = self._categorize_error(error)
            analyzed_errors.append({
                'type': error_type,
                'message': error,
                'severity': 'high'
            })
        
        for warning in validation_result.warnings:
            warning_type = self._categorize_error(warning)
            analyzed_errors.append({
                'type': warning_type,
                'message': warning,
                'severity': 'medium'
            })
        
        return analyzed_errors
    
    def _categorize_error(self, error_message: str) -> str:
        """Enhanced error categorization based on message content"""
        error_lower = error_message.lower()

        if 'contradiction' in error_lower:
            return 'contradiction'
        elif 'circular' in error_lower:
            return 'circular_reasoning'
        elif 'unsupported' in error_lower or 'support' in error_lower:
            return 'unsupported_conclusion'
        elif 'inference' in error_lower or 'reasoning' in error_lower:
            return 'invalid_inference'
        elif 'rule' in error_lower or 'violation' in error_lower:
            return 'rule_violation'
        elif 'domain' in error_lower or 'specific' in error_lower:
            return 'domain_specific'
        elif 'evidence' in error_lower:
            return 'insufficient_evidence'
        elif 'connection' in error_lower or 'weak' in error_lower:
            return 'weak_connection'
        else:
            return 'general_logic_error'
    
    def generate_correction_suggestions(self, analyzed_errors: List[Dict[str, Any]]) -> List[RefinementProposal]:
        """Generate specific correction suggestions"""
        proposals = []
        
        for error in analyzed_errors:
            error_type = error['type']
            severity = error['severity']
            
            # Determine priority based on severity
            priority = 1 if severity == 'high' else 2 if severity == 'medium' else 3
            
            suggestion = self.suggestion_templates.get(
                error_type, 
                "Review and revise the logical structure of your response"
            )
            
            proposal = RefinementProposal(
                issue=error['message'],
                suggestion=suggestion,
                priority=priority
            )
            proposals.append(proposal)
        
        return proposals
    
    def format_refinement_prompt(self, suggestions: List[RefinementProposal], query_text: str = None) -> str:
        """Format refinement suggestions into a coherent prompt with domain awareness"""
        if not suggestions:
            return "Please review your response for logical consistency."

        # Sort by priority
        sorted_suggestions = sorted(suggestions, key=lambda x: x.priority)

        # Determine domain for domain-specific guidance
        domain = self._detect_domain(query_text) if query_text else None
        domain_guidance = ""

        if domain and domain in self.domain_strategies:
            strategy = self.domain_strategies[domain]
            domain_guidance = f"\n\nDomain-Specific Focus: {strategy['focus']}\n"
            domain_guidance += "Recommendations:\n"
            for i, sugg in enumerate(strategy['suggestions'], 1):
                domain_guidance += f"- {sugg}\n"

        prompt_parts = ["Please address the following logical issues:"]

        for i, suggestion in enumerate(sorted_suggestions, 1):
            priority_label = ["HIGH", "MEDIUM", "LOW"][suggestion.priority - 1]
            prompt_parts.append(
                f"{i}. [{priority_label}] {suggestion.issue}\n"
                f"   Suggestion: {suggestion.suggestion}"
            )

        if domain_guidance:
            prompt_parts.append(domain_guidance)

        prompt_parts.append(
            "\nPlease revise your response to address these issues while "
            "maintaining the core information and improving logical consistency. "
            "Ensure your reasoning is valid and well-structured."
        )

        return "\n\n".join(prompt_parts)

    def _detect_domain(self, query_text: str) -> str:
        """Detect the domain of the query for specialized refinement strategies"""
        if not query_text:
            return None

        query_lower = query_text.lower()

        # Mathematics keywords
        math_keywords = ['equation', 'solve', 'calculate', 'number', 'algebra', 'geometry',
                        'theorem', 'proof', 'derivative', 'integral', 'function']

        # Science keywords
        science_keywords = ['experiment', 'hypothesis', 'theory', 'observation', 'data',
                           'analysis', 'scientific', 'research', 'study', 'evidence']

        # Logic keywords
        logic_keywords = ['if', 'then', 'implies', 'therefore', 'because', 'since',
                         'all', 'some', 'every', 'exists', 'not', 'and', 'or']

        # Count keyword matches
        math_score = sum(1 for keyword in math_keywords if keyword in query_lower)
        science_score = sum(1 for keyword in science_keywords if keyword in query_lower)
        logic_score = sum(1 for keyword in logic_keywords if keyword in query_lower)

        # Determine domain based on highest score
        scores = {
            'mathematics': math_score,
            'science': science_score,
            'logic': logic_score
        }

        max_domain = max(scores, key=scores.get)
        if scores[max_domain] > 0:
            return max_domain

        return None
