from typing import Dict, Any
import os

class Config:
    # LLM Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4")
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
    
    # Validation Configuration
    MAX_REFINEMENT_ITERATIONS = int(os.getenv("MAX_REFINEMENT_ITERATIONS", "3"))
    VALIDATION_TIMEOUT = int(os.getenv("VALIDATION_TIMEOUT", "30"))
    
    # PyKe Configuration
    PYKE_RULES_DIR = os.getenv("PYKE_RULES_DIR", "./rules")
    
    # API Configuration
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8000"))
